@echo off
REM 大数据实时处理任务运行脚本 (Windows版本)
REM 作者：学生姓名
REM 日期：%date%

echo ==========================================
echo 大数据实时处理技术期末考试任务
echo ==========================================

REM 设置环境变量 (请根据实际安装路径调整)
set SPARK_HOME=C:\spark
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX

REM 检查Spark环境
if not exist "%SPARK_HOME%" (
    echo 错误：未找到Spark安装目录，请检查SPARK_HOME环境变量
    pause
    exit /b 1
)

echo Spark安装路径：%SPARK_HOME%
echo Java安装路径：%JAVA_HOME%
echo.

REM 第一部分：RDD编程统计人口平均年龄
echo 第一部分：RDD编程统计人口平均年龄
echo ==========================================

echo 1. 生成模拟人口年龄数据...
"%SPARK_HOME%\bin\spark-submit" ^
    --class GeneratePeopleAge ^
    --master local[*] ^
    --driver-memory 2g ^
    --executor-memory 2g ^
    GeneratePeopleAge.scala

if %errorlevel% equ 0 (
    echo ✓ 人口年龄数据生成成功
) else (
    echo ✗ 人口年龄数据生成失败
    pause
    exit /b 1
)

echo.
echo 2. 计算平均年龄...
"%SPARK_HOME%\bin\spark-submit" ^
    --class CalculateAverageAge ^
    --master local[*] ^
    --driver-memory 2g ^
    --executor-memory 2g ^
    CalculateAverageAge.scala

if %errorlevel% equ 0 (
    echo ✓ 平均年龄计算成功
) else (
    echo ✗ 平均年龄计算失败
    pause
    exit /b 1
)

echo.

REM 第二部分：基于咖啡连锁店的Spark数据处理分析
echo 第二部分：基于咖啡连锁店的Spark数据处理分析
echo ==========================================

REM 检查数据文件是否存在
if not exist "CoffeeChain.csv" (
    echo 错误：未找到CoffeeChain.csv数据文件
    pause
    exit /b 1
)

echo 3. 执行咖啡连锁店数据分析...
"%SPARK_HOME%\bin\spark-submit" ^
    --class CoffeeChainAnalysis ^
    --master local[*] ^
    --driver-memory 4g ^
    --executor-memory 4g ^
    --conf spark.sql.adaptive.enabled=true ^
    --conf spark.sql.adaptive.coalescePartitions.enabled=true ^
    CoffeeChainAnalysis.scala

if %errorlevel% equ 0 (
    echo ✓ 咖啡连锁店数据分析成功
) else (
    echo ✗ 咖啡连锁店数据分析失败
    pause
    exit /b 1
)

echo.
echo ==========================================
echo 所有任务执行完成！
echo ==========================================

REM 显示生成的文件和目录
echo 生成的文件和目录：
echo 1. 人口年龄数据：
if exist "peopleage.txt" (
    dir peopleage.txt*
) else (
    echo    未找到peopleage.txt文件
)

if exist "average_age_result.txt" (
    dir average_age_result.txt*
) else (
    echo    未找到average_age_result.txt文件
)

echo.
echo 2. 咖啡分析结果：
if exist "product_ranking" (
    echo    ✓ product_ranking目录已生成
) else (
    echo    未找到product_ranking目录
)

if exist "state_ranking" (
    echo    ✓ state_ranking目录已生成
) else (
    echo    未找到state_ranking目录
)

if exist "sales_by_state" (
    echo    ✓ sales_by_state目录已生成
) else (
    echo    未找到sales_by_state目录
)

if exist "sales_by_market" (
    echo    ✓ sales_by_market目录已生成
) else (
    echo    未找到sales_by_market目录
)

if exist "profit_by_product_type" (
    echo    ✓ profit_by_product_type目录已生成
) else (
    echo    未找到profit_by_product_type目录
)

if exist "market_analysis" (
    echo    ✓ market_analysis目录已生成
) else (
    echo    未找到market_analysis目录
)

if exist "analysis_report" (
    echo    ✓ analysis_report目录已生成
) else (
    echo    未找到analysis_report目录
)

echo.
echo 任务完成时间：%date% %time%
echo 请查看生成的结果文件进行验证。
echo.
pause
