import org.apache.spark.sql.SparkSession
import scala.util.Random

/**
 * 生成模拟人口年龄数据文件
 * 功能：在本地文件系统中生成数据文件peopleage.txt
 * 数据格式：每行包含两列数据，第1列是序号，第2列是年龄
 */
object GeneratePeopleAge {
  
  def main(args: Array[String]): Unit = {
    
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("GeneratePeopleAge")
      .master("local[*]")  // 本地模式运行
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    // 设置日志级别为WARN，减少输出信息
    sc.setLogLevel("WARN")
    
    println("开始生成人口年龄数据...")
    
    // 配置参数
    val numRecords = 1000  // 生成1000条记录
    val outputPath = "peopleage.txt"  // 输出文件路径
    
    // 生成随机年龄数据（18-80岁之间）
    val random = new Random()
    val ageData = (1 to numRecords).map { id =>
      val age = 18 + random.nextInt(63)  // 生成18-80岁之间的随机年龄
      s"$id\t$age"  // 格式：序号\t年龄
    }
    
    // 将数据转换为RDD
    val ageRDD = sc.parallelize(ageData)
    
    // 保存到本地文件系统
    ageRDD.coalesce(1).saveAsTextFile(outputPath)
    
    println(s"数据生成完成！")
    println(s"文件保存路径：$outputPath")
    println(s"记录总数：$numRecords")
    
    // 显示前10条数据作为示例
    println("\n前10条数据示例：")
    ageRDD.take(10).foreach(println)
    
    // 统计年龄分布
    val ageStats = ageRDD.map(line => {
      val parts = line.split("\t")
      parts(1).toInt
    })
    
    val minAge = ageStats.min()
    val maxAge = ageStats.max()
    val avgAge = ageStats.mean()
    
    println(s"\n数据统计信息：")
    println(s"最小年龄：$minAge")
    println(s"最大年龄：$maxAge")
    println(s"平均年龄：${avgAge.formatted("%.2f")}")
    
    // 关闭SparkSession
    spark.stop()
    
    println("\n程序执行完成！")
  }
}
