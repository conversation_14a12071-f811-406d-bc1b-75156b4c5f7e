# 大数据实时处理任务运行指南

## 环境准备

### 1. 检查Spark环境
```bash
# 检查Spark是否安装
ls -la /opt/spark

# 检查Java环境
java -version
```

### 2. 检查工作目录
确保在正确的工作目录中：
```bash
pwd
# 应该显示：/home/<USER>/spark02

# 查看当前目录文件
ls -la
```

## 运行步骤

### 步骤1：测试Spark环境（推荐先执行）
```bash
# 给脚本添加执行权限
chmod +x test_spark_shell.sh

# 运行测试
./test_spark_shell.sh
```

如果测试成功，会显示：
```
✓ Spark Shell环境测试成功！
```

### 步骤2：运行完整分析程序
```bash
# 给主脚本添加执行权限
chmod +x run_analysis.sh

# 运行完整分析
./run_analysis.sh
```

## 手动运行方式

如果脚本运行有问题，可以手动执行每个部分：

### 第一部分：生成人口年龄数据
```bash
/opt/spark/bin/spark-shell --master local[*] --driver-memory 2g
```

然后在spark-shell中输入：
```scala
import scala.util.Random

println("开始生成人口年龄数据...")

val numRecords = 1000
val outputPath = "peopleage.txt"

val random = new Random()
val ageData = (1 to numRecords).map { id =>
  val age = 18 + random.nextInt(63)
  s"$id\t$age"
}

val ageRDD = sc.parallelize(ageData)
ageRDD.coalesce(1).saveAsTextFile(outputPath)

println(s"数据生成完成！文件保存路径：$outputPath")
ageRDD.take(10).foreach(println)

val ageStats = ageRDD.map(line => line.split("\t")(1).toInt)
println(s"平均年龄：${ageStats.mean().formatted("%.2f")}")

:quit
```

### 第二部分：计算平均年龄
```bash
/opt/spark/bin/spark-shell --master local[*] --driver-memory 2g
```

然后在spark-shell中输入：
```scala
println("开始计算人口平均年龄...")

val inputPath = "peopleage.txt"
val dataRDD = sc.textFile(inputPath)

val ageRDD = dataRDD.map(line => {
  val parts = line.split("\t")
  parts(1).toInt
})

ageRDD.cache()

val totalCount = ageRDD.count()
val totalAge = ageRDD.reduce(_ + _)
val averageAge = totalAge.toDouble / totalCount

println(s"总人口数量：$totalCount")
println(s"平均年龄：${averageAge.formatted("%.2f")}岁")

val resultData = Seq(
  s"人口年龄统计分析结果",
  s"总人口数量：$totalCount",
  s"平均年龄：${averageAge.formatted("%.2f")}岁"
)

sc.parallelize(resultData).coalesce(1).saveAsTextFile("average_age_result.txt")
println("分析结果已保存")

:quit
```

### 第三部分：咖啡数据分析
```bash
/opt/spark/bin/spark-shell --master local[*] --driver-memory 4g
```

然后在spark-shell中输入：
```scala
import org.apache.spark.sql.functions._

println("开始咖啡连锁店数据分析...")

val inputPath = "CoffeeChain.csv"
val rawDF = spark.read.option("header", "true").option("inferSchema", "true").csv(inputPath)

println("数据概览：")
rawDF.show(5)

val coffeeDF = rawDF.filter(col("Coffee Sales").isNotNull && col("Coffee Sales") > 0)
coffeeDF.cache()

println(s"有效记录数：${coffeeDF.count()}")

// 按产品排名
val productRanking = coffeeDF
  .groupBy("Product", "Product Type")
  .agg(sum("Coffee Sales").alias("Total_Sales"))
  .orderBy(desc("Total_Sales"))

println("按产品销售量排名：")
productRanking.show(10)

// 按州排名
val stateRanking = coffeeDF
  .groupBy("State")
  .agg(sum("Coffee Sales").alias("Total_Sales"))
  .orderBy(desc("Total_Sales"))

println("按州销售量排名：")
stateRanking.show()

// 保存结果
productRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("product_ranking")
stateRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("state_ranking")

println("分析结果已保存")

:quit
```

## 预期输出文件

运行成功后，应该生成以下文件和目录：

### 第一部分输出
- `peopleage.txt/` - 人口年龄数据
- `average_age_result.txt/` - 平均年龄分析结果

### 第二部分输出
- `product_ranking/` - 产品销售排名
- `state_ranking/` - 州销售排名
- `sales_by_state/` - 按州销售分析
- `sales_by_market/` - 按市场销售分析
- `profit_by_product_type/` - 按产品类型利润分析
- `market_analysis/` - 市场规模分析
- `analysis_report/` - 综合分析报告

## 故障排除

### 常见问题

1. **Java路径错误**
   ```bash
   # 查找正确的Java路径
   find /usr -name "java" -type f 2>/dev/null | grep bin
   
   # 设置正确的JAVA_HOME
   export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
   ```

2. **Spark启动失败**
   ```bash
   # 检查Spark目录
   ls -la /opt/spark/bin/
   
   # 直接测试spark-shell
   /opt/spark/bin/spark-shell --version
   ```

3. **内存不足**
   ```bash
   # 减少内存配置
   /opt/spark/bin/spark-shell --master local[*] --driver-memory 1g
   ```

4. **文件权限问题**
   ```bash
   # 检查文件权限
   ls -la *.sh
   
   # 添加执行权限
   chmod +x *.sh
   ```

### 调试技巧

1. **查看详细日志**：
   在spark-shell中设置日志级别：
   ```scala
   sc.setLogLevel("INFO")
   ```

2. **检查数据文件**：
   ```bash
   head -5 CoffeeChain.csv
   wc -l CoffeeChain.csv
   ```

3. **验证输出结果**：
   ```bash
   # 查看生成的目录
   ls -la *ranking* *analysis* *result*

   # 查看文件内容
   cat peopleage.txt/part-00000 | head -10
   ```

## 查看结果指令大全

### 第一部分：人口年龄统计结果查看

#### 1. 查看生成的人口年龄数据
```bash
# 查看peopleage.txt目录结构
ls -la peopleage.txt/

# 查看人口年龄数据内容（前20行）
head -20 peopleage.txt/part-00000

# 查看人口年龄数据内容（后10行）
tail -10 peopleage.txt/part-00000

# 统计数据行数
wc -l peopleage.txt/part-00000

# 查看完整文件内容
cat peopleage.txt/part-00000
```

#### 2. 查看平均年龄计算结果
```bash
# 查看average_age_result.txt目录结构
ls -la average_age_result.txt/

# 查看平均年龄分析报告
cat average_age_result.txt/part-00000

# 查看分析报告的前几行
head -10 average_age_result.txt/part-00000
```

### 第二部分：咖啡连锁店数据分析结果查看

#### 1. 查看产品销售排名
```bash
# 查看产品排名目录
ls -la product_ranking/

# 查看产品销售排名结果
cat product_ranking/part-00000

# 查看产品排名前10名
head -11 product_ranking/part-00000  # 包含表头

# 使用column命令格式化显示
column -t -s',' product_ranking/part-00000 | head -15
```

#### 2. 查看州销售排名
```bash
# 查看州排名目录
ls -la state_ranking/

# 查看州销售排名结果
cat state_ranking/part-00000

# 查看州排名前10名
head -11 state_ranking/part-00000

# 格式化显示州排名
column -t -s',' state_ranking/part-00000
```

#### 3. 查看按州销售分析
```bash
# 查看按州销售分析目录
ls -la sales_by_state/

# 查看按州销售分析结果
cat sales_by_state/part-00000

# 格式化显示
column -t -s',' sales_by_state/part-00000
```

#### 4. 查看按市场销售分析
```bash
# 查看按市场销售分析目录
ls -la sales_by_market/

# 查看按市场销售分析结果
cat sales_by_market/part-00000

# 格式化显示
column -t -s',' sales_by_market/part-00000
```

#### 5. 查看按产品类型利润分析
```bash
# 查看按产品类型利润分析目录
ls -la profit_by_product_type/

# 查看按产品类型利润分析结果
cat profit_by_product_type/part-00000

# 格式化显示
column -t -s',' profit_by_product_type/part-00000
```

#### 6. 查看市场规模分析
```bash
# 查看市场规模分析目录
ls -la market_analysis/

# 查看市场规模分析结果
cat market_analysis/part-00000

# 格式化显示
column -t -s',' market_analysis/part-00000
```

#### 7. 查看综合分析报告
```bash
# 查看综合分析报告目录
ls -la analysis_report/

# 查看综合分析报告内容
cat analysis_report/part-00000

# 逐页查看报告
less analysis_report/part-00000
```

### 一键查看所有结果

#### 快速概览所有生成的文件
```bash
# 查看所有生成的目录和文件
echo "=== 生成的所有文件和目录 ==="
ls -la | grep -E "(peopleage|average_age|ranking|analysis|sales_by|profit_by|market_analysis)"

echo -e "\n=== 文件大小统计 ==="
du -sh peopleage.txt* average_age_result.txt* *ranking* *analysis* sales_by* profit_by* market_analysis* 2>/dev/null
```

#### 查看所有结果的摘要信息
```bash
#!/bin/bash
echo "=========================================="
echo "大数据实时处理任务结果摘要"
echo "=========================================="

echo -e "\n【第一部分：人口年龄统计】"
if [ -f "peopleage.txt/part-00000" ]; then
    echo "✓ 人口年龄数据：$(wc -l < peopleage.txt/part-00000) 条记录"
    echo "  数据示例："
    head -3 peopleage.txt/part-00000 | sed 's/^/    /'
else
    echo "✗ 未找到人口年龄数据文件"
fi

if [ -f "average_age_result.txt/part-00000" ]; then
    echo "✓ 平均年龄分析报告已生成"
    echo "  报告摘要："
    head -5 average_age_result.txt/part-00000 | sed 's/^/    /'
else
    echo "✗ 未找到平均年龄分析报告"
fi

echo -e "\n【第二部分：咖啡连锁店数据分析】"
if [ -f "product_ranking/part-00000" ]; then
    echo "✓ 产品销售排名：$(wc -l < product_ranking/part-00000) 条记录"
    echo "  销量前3名产品："
    head -4 product_ranking/part-00000 | tail -3 | sed 's/^/    /'
else
    echo "✗ 未找到产品销售排名文件"
fi

if [ -f "state_ranking/part-00000" ]; then
    echo "✓ 州销售排名：$(wc -l < state_ranking/part-00000) 条记录"
    echo "  销量前3名州："
    head -4 state_ranking/part-00000 | tail -3 | sed 's/^/    /'
else
    echo "✗ 未找到州销售排名文件"
fi

if [ -f "analysis_report/part-00000" ]; then
    echo "✓ 综合分析报告已生成"
    echo "  报告内容："
    cat analysis_report/part-00000 | sed 's/^/    /'
else
    echo "✗ 未找到综合分析报告"
fi

echo -e "\n=========================================="
echo "结果查看完成！"
echo "=========================================="
```

#### 保存上述脚本为查看结果的快捷命令
```bash
# 将上述脚本保存为文件
cat > view_results.sh << 'EOF'
#!/bin/bash
echo "=========================================="
echo "大数据实时处理任务结果摘要"
echo "=========================================="

echo -e "\n【第一部分：人口年龄统计】"
if [ -f "peopleage.txt/part-00000" ]; then
    echo "✓ 人口年龄数据：$(wc -l < peopleage.txt/part-00000) 条记录"
    echo "  数据示例："
    head -3 peopleage.txt/part-00000 | sed 's/^/    /'
else
    echo "✗ 未找到人口年龄数据文件"
fi

if [ -f "average_age_result.txt/part-00000" ]; then
    echo "✓ 平均年龄分析报告已生成"
    echo "  报告摘要："
    head -5 average_age_result.txt/part-00000 | sed 's/^/    /'
else
    echo "✗ 未找到平均年龄分析报告"
fi

echo -e "\n【第二部分：咖啡连锁店数据分析】"
if [ -f "product_ranking/part-00000" ]; then
    echo "✓ 产品销售排名：$(wc -l < product_ranking/part-00000) 条记录"
    echo "  销量前3名产品："
    head -4 product_ranking/part-00000 | tail -3 | sed 's/^/    /'
else
    echo "✗ 未找到产品销售排名文件"
fi

if [ -f "state_ranking/part-00000" ]; then
    echo "✓ 州销售排名：$(wc -l < state_ranking/part-00000) 条记录"
    echo "  销量前3名州："
    head -4 state_ranking/part-00000 | tail -3 | sed 's/^/    /'
else
    echo "✗ 未找到州销售排名文件"
fi

if [ -f "analysis_report/part-00000" ]; then
    echo "✓ 综合分析报告已生成"
    echo "  报告内容："
    cat analysis_report/part-00000 | sed 's/^/    /'
else
    echo "✗ 未找到综合分析报告"
fi

echo -e "\n=========================================="
echo "结果查看完成！"
echo "=========================================="
EOF

# 给脚本添加执行权限
chmod +x view_results.sh

# 运行查看结果
./view_results.sh
```

### 高级查看技巧

#### 1. 使用grep查找特定内容
```bash
# 在产品排名中查找特定产品
grep -i "coffee" product_ranking/part-00000

# 在州排名中查找特定州
grep -i "california\|texas\|florida" state_ranking/part-00000
```

#### 2. 使用awk进行数据处理
```bash
# 计算产品排名中的平均销售额
awk -F',' 'NR>1 {sum+=$3; count++} END {print "平均销售额:", sum/count}' product_ranking/part-00000

# 统计各州的记录数
awk -F',' 'NR>1 {count[$1]++} END {for(state in count) print state, count[state]}' state_ranking/part-00000
```

#### 3. 使用sort进行排序查看
```bash
# 按销售额排序查看产品排名
sort -t',' -k3 -nr product_ranking/part-00000

# 按州名字母顺序查看州排名
sort -t',' -k1 state_ranking/part-00000
```

## 联系支持

如果遇到问题，请检查：
1. Spark环境是否正确安装
2. Java版本是否兼容（推荐Java 8）
3. 工作目录是否正确
4. 数据文件是否存在

按照本指南逐步执行，应该能够成功完成所有任务。
