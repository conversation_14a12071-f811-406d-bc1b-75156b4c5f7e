# 大数据实时处理任务运行指南

## 环境准备

### 1. 检查Spark环境
```bash
# 检查Spark是否安装
ls -la /opt/spark

# 检查Java环境
java -version
```

### 2. 检查工作目录
确保在正确的工作目录中：
```bash
pwd
# 应该显示：/home/<USER>/spark02

# 查看当前目录文件
ls -la
```

## 运行步骤

### 步骤1：测试Spark环境（推荐先执行）
```bash
# 给脚本添加执行权限
chmod +x test_spark_shell.sh

# 运行测试
./test_spark_shell.sh
```

如果测试成功，会显示：
```
✓ Spark Shell环境测试成功！
```

### 步骤2：运行完整分析程序
```bash
# 给主脚本添加执行权限
chmod +x run_analysis.sh

# 运行完整分析
./run_analysis.sh
```

## 手动运行方式

如果脚本运行有问题，可以手动执行每个部分：

### 第一部分：生成人口年龄数据
```bash
/opt/spark/bin/spark-shell --master local[*] --driver-memory 2g
```

然后在spark-shell中输入：
```scala
import scala.util.Random

println("开始生成人口年龄数据...")

val numRecords = 1000
val outputPath = "peopleage.txt"

val random = new Random()
val ageData = (1 to numRecords).map { id =>
  val age = 18 + random.nextInt(63)
  s"$id\t$age"
}

val ageRDD = sc.parallelize(ageData)
ageRDD.coalesce(1).saveAsTextFile(outputPath)

println(s"数据生成完成！文件保存路径：$outputPath")
ageRDD.take(10).foreach(println)

val ageStats = ageRDD.map(line => line.split("\t")(1).toInt)
println(s"平均年龄：${ageStats.mean().formatted("%.2f")}")

:quit
```

### 第二部分：计算平均年龄
```bash
/opt/spark/bin/spark-shell --master local[*] --driver-memory 2g
```

然后在spark-shell中输入：
```scala
println("开始计算人口平均年龄...")

val inputPath = "peopleage.txt"
val dataRDD = sc.textFile(inputPath)

val ageRDD = dataRDD.map(line => {
  val parts = line.split("\t")
  parts(1).toInt
})

ageRDD.cache()

val totalCount = ageRDD.count()
val totalAge = ageRDD.reduce(_ + _)
val averageAge = totalAge.toDouble / totalCount

println(s"总人口数量：$totalCount")
println(s"平均年龄：${averageAge.formatted("%.2f")}岁")

val resultData = Seq(
  s"人口年龄统计分析结果",
  s"总人口数量：$totalCount",
  s"平均年龄：${averageAge.formatted("%.2f")}岁"
)

sc.parallelize(resultData).coalesce(1).saveAsTextFile("average_age_result.txt")
println("分析结果已保存")

:quit
```

### 第三部分：咖啡数据分析
```bash
/opt/spark/bin/spark-shell --master local[*] --driver-memory 4g
```

然后在spark-shell中输入：
```scala
import org.apache.spark.sql.functions._

println("开始咖啡连锁店数据分析...")

val inputPath = "CoffeeChain.csv"
val rawDF = spark.read.option("header", "true").option("inferSchema", "true").csv(inputPath)

println("数据概览：")
rawDF.show(5)

val coffeeDF = rawDF.filter(col("Coffee Sales").isNotNull && col("Coffee Sales") > 0)
coffeeDF.cache()

println(s"有效记录数：${coffeeDF.count()}")

// 按产品排名
val productRanking = coffeeDF
  .groupBy("Product", "Product Type")
  .agg(sum("Coffee Sales").alias("Total_Sales"))
  .orderBy(desc("Total_Sales"))

println("按产品销售量排名：")
productRanking.show(10)

// 按州排名
val stateRanking = coffeeDF
  .groupBy("State")
  .agg(sum("Coffee Sales").alias("Total_Sales"))
  .orderBy(desc("Total_Sales"))

println("按州销售量排名：")
stateRanking.show()

// 保存结果
productRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("product_ranking")
stateRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("state_ranking")

println("分析结果已保存")

:quit
```

## 预期输出文件

运行成功后，应该生成以下文件和目录：

### 第一部分输出
- `peopleage.txt/` - 人口年龄数据
- `average_age_result.txt/` - 平均年龄分析结果

### 第二部分输出
- `product_ranking/` - 产品销售排名
- `state_ranking/` - 州销售排名
- `sales_by_state/` - 按州销售分析
- `sales_by_market/` - 按市场销售分析
- `profit_by_product_type/` - 按产品类型利润分析
- `market_analysis/` - 市场规模分析
- `analysis_report/` - 综合分析报告

## 故障排除

### 常见问题

1. **Java路径错误**
   ```bash
   # 查找正确的Java路径
   find /usr -name "java" -type f 2>/dev/null | grep bin
   
   # 设置正确的JAVA_HOME
   export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
   ```

2. **Spark启动失败**
   ```bash
   # 检查Spark目录
   ls -la /opt/spark/bin/
   
   # 直接测试spark-shell
   /opt/spark/bin/spark-shell --version
   ```

3. **内存不足**
   ```bash
   # 减少内存配置
   /opt/spark/bin/spark-shell --master local[*] --driver-memory 1g
   ```

4. **文件权限问题**
   ```bash
   # 检查文件权限
   ls -la *.sh
   
   # 添加执行权限
   chmod +x *.sh
   ```

### 调试技巧

1. **查看详细日志**：
   在spark-shell中设置日志级别：
   ```scala
   sc.setLogLevel("INFO")
   ```

2. **检查数据文件**：
   ```bash
   head -5 CoffeeChain.csv
   wc -l CoffeeChain.csv
   ```

3. **验证输出结果**：
   ```bash
   # 查看生成的目录
   ls -la *ranking* *analysis* *result*
   
   # 查看文件内容
   cat peopleage.txt/part-00000 | head -10
   ```

## 联系支持

如果遇到问题，请检查：
1. Spark环境是否正确安装
2. Java版本是否兼容（推荐Java 8）
3. 工作目录是否正确
4. 数据文件是否存在

按照本指南逐步执行，应该能够成功完成所有任务。
