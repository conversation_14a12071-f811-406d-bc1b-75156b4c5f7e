# 查看结果指令参考卡

## 🚀 快速查看（推荐）

```bash
# 一键查看所有结果摘要
chmod +x view_results.sh
./view_results.sh
```

## 📊 第一部分：人口年龄统计结果

### 查看人口年龄数据
```bash
# 查看目录结构
ls -la peopleage.txt/

# 查看数据内容（前20行）
head -20 peopleage.txt/part-00000

# 查看数据内容（后10行）  
tail -10 peopleage.txt/part-00000

# 统计总行数
wc -l peopleage.txt/part-00000

# 查看完整内容
cat peopleage.txt/part-00000
```

### 查看平均年龄分析报告
```bash
# 查看完整报告
cat average_age_result.txt/part-00000

# 查看报告前几行
head -10 average_age_result.txt/part-00000
```

## 📈 第二部分：咖啡连锁店数据分析结果

### 1. 产品销售排名
```bash
# 查看完整排名
cat product_ranking/part-00000

# 查看前10名（包含表头）
head -11 product_ranking/part-00000

# 格式化显示
column -t -s',' product_ranking/part-00000 | head -15

# 搜索特定产品
grep -i "coffee" product_ranking/part-00000
```

### 2. 州销售排名
```bash
# 查看完整排名
cat state_ranking/part-00000

# 查看前10名
head -11 state_ranking/part-00000

# 格式化显示
column -t -s',' state_ranking/part-00000

# 搜索特定州
grep -i "california\|texas\|florida" state_ranking/part-00000
```

### 3. 按州销售分析
```bash
# 查看完整分析
cat sales_by_state/part-00000

# 格式化显示
column -t -s',' sales_by_state/part-00000
```

### 4. 按市场销售分析
```bash
# 查看完整分析
cat sales_by_market/part-00000

# 格式化显示
column -t -s',' sales_by_market/part-00000
```

### 5. 按产品类型利润分析
```bash
# 查看完整分析
cat profit_by_product_type/part-00000

# 格式化显示
column -t -s',' profit_by_product_type/part-00000
```

### 6. 市场规模分析
```bash
# 查看完整分析
cat market_analysis/part-00000

# 格式化显示
column -t -s',' market_analysis/part-00000
```

### 7. 综合分析报告
```bash
# 查看完整报告
cat analysis_report/part-00000

# 逐页查看
less analysis_report/part-00000
```

## 🔍 高级查看技巧

### 使用grep搜索
```bash
# 在产品排名中搜索咖啡相关产品
grep -i "coffee\|espresso\|latte" product_ranking/part-00000

# 在州排名中搜索特定地区
grep -E "(California|Texas|New York)" state_ranking/part-00000

# 搜索高销售额记录（假设销售额在第3列）
awk -F',' '$3 > 1000' product_ranking/part-00000
```

### 使用awk进行数据分析
```bash
# 计算产品排名中的平均销售额（第3列）
awk -F',' 'NR>1 {sum+=$3; count++} END {print "平均销售额:", sum/count}' product_ranking/part-00000

# 统计各州的记录数
awk -F',' 'NR>1 {count[$1]++} END {for(state in count) print state, count[state]}' state_ranking/part-00000

# 找出销售额最高的记录
awk -F',' 'NR>1 {if($3>max) {max=$3; line=$0}} END {print "最高销售额记录:", line}' product_ranking/part-00000
```

### 使用sort排序
```bash
# 按销售额降序排列（第3列）
sort -t',' -k3 -nr product_ranking/part-00000

# 按州名字母顺序排列
sort -t',' -k1 state_ranking/part-00000

# 按利润降序排列（假设利润在第4列）
sort -t',' -k4 -nr profit_by_product_type/part-00000
```

### 统计和计算
```bash
# 统计文件行数（不包含表头）
tail -n +2 product_ranking/part-00000 | wc -l

# 计算某列的总和（第3列销售额）
awk -F',' 'NR>1 {sum+=$3} END {print "总销售额:", sum}' product_ranking/part-00000

# 找出包含特定关键词的行数
grep -c -i "coffee" product_ranking/part-00000
```

## 📋 一键查看所有文件

### 快速概览
```bash
# 查看所有生成的目录
ls -la | grep -E "(peopleage|average_age|ranking|analysis|sales_by|profit_by|market_analysis)"

# 查看文件大小
du -sh peopleage.txt* average_age_result.txt* *ranking* *analysis* sales_by* profit_by* market_analysis* 2>/dev/null
```

### 批量查看文件头部
```bash
# 查看所有CSV文件的前3行
for file in */part-00000; do
    echo "=== $file ==="
    head -3 "$file"
    echo ""
done
```

### 生成完整报告
```bash
# 创建完整的结果报告
cat > full_report.txt << EOF
大数据实时处理任务完整结果报告
生成时间: $(date)
工作目录: $(pwd)

=== 第一部分：人口年龄统计 ===
$(cat peopleage.txt/part-00000 2>/dev/null | head -10)

平均年龄分析:
$(cat average_age_result.txt/part-00000 2>/dev/null)

=== 第二部分：咖啡数据分析 ===
产品销售排名前5:
$(head -6 product_ranking/part-00000 2>/dev/null | tail -5)

州销售排名前5:
$(head -6 state_ranking/part-00000 2>/dev/null | tail -5)

综合分析报告:
$(cat analysis_report/part-00000 2>/dev/null)
EOF

echo "完整报告已生成: full_report.txt"
```

## 🛠️ 故障排除

### 文件不存在
```bash
# 检查文件是否存在
if [ ! -f "peopleage.txt/part-00000" ]; then
    echo "人口年龄数据文件不存在，请先运行生成程序"
fi

# 查找所有part文件
find . -name "part-*" -type f
```

### 权限问题
```bash
# 检查文件权限
ls -la */part-00000

# 修改权限（如果需要）
chmod 644 */part-00000
```

### 文件格式问题
```bash
# 检查文件编码
file peopleage.txt/part-00000

# 查看文件的十六进制内容（检查特殊字符）
hexdump -C peopleage.txt/part-00000 | head -5
```

## 💡 使用建议

1. **首次查看**：使用 `./view_results.sh` 获得整体概览
2. **详细分析**：使用具体的cat或column命令查看感兴趣的文件
3. **数据验证**：使用wc、head、tail命令验证数据完整性
4. **格式化显示**：使用column命令让CSV数据更易读
5. **搜索分析**：使用grep和awk进行特定数据的搜索和分析

## 📞 常用命令速查

| 功能 | 命令 |
|------|------|
| 查看文件内容 | `cat 文件名` |
| 查看前N行 | `head -n N 文件名` |
| 查看后N行 | `tail -n N 文件名` |
| 统计行数 | `wc -l 文件名` |
| 格式化CSV | `column -t -s',' 文件名` |
| 搜索内容 | `grep '关键词' 文件名` |
| 排序 | `sort 文件名` |
| 逐页查看 | `less 文件名` |
