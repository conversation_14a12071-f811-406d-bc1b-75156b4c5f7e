# 大数据实时处理技术期末考试任务

## 项目概述

本项目完成山东协和学院《大数据实时处理技术》期末考试的两个主要任务：

1. **RDD编程统计人口平均年龄**
2. **基于咖啡连锁店的Spark数据处理分析**

## 文件结构

```
lbxx3/
├── CoffeeChain.csv              # 咖啡连锁店原始数据
├── GeneratePeopleAge.scala      # 生成人口年龄数据程序
├── CalculateAverageAge.scala    # 计算平均年龄程序
├── CoffeeChainAnalysis.scala    # 咖啡连锁店数据分析程序
├── run_analysis.sh              # 运行脚本
├── 任务要求.md                   # 任务要求说明
├── 实训报告模版.md               # 完整实训报告
└── README.md                    # 本说明文件
```

## 环境要求

- **操作系统**: CentOS 7+ (推荐在hadoop03节点运行)
- **Java**: JDK 8+
- **Spark**: Apache Spark 2.4+
- **Scala**: Scala 2.11+

## 快速开始

### 方法一：使用运行脚本（推荐）

1. 确保所有文件在工作目录 `/home/<USER>/spark02/`
2. 给脚本添加执行权限：
   ```bash
   chmod +x run_analysis.sh
   ```
3. 运行脚本：
   ```bash
   ./run_analysis.sh
   ```

### 方法二：手动运行

#### 第一部分：人口年龄统计

1. **生成人口年龄数据**：
   ```bash
   $SPARK_HOME/bin/spark-submit \
     --class GeneratePeopleAge \
     --master local[*] \
     GeneratePeopleAge.scala
   ```

2. **计算平均年龄**：
   ```bash
   $SPARK_HOME/bin/spark-submit \
     --class CalculateAverageAge \
     --master local[*] \
     CalculateAverageAge.scala
   ```

#### 第二部分：咖啡连锁店数据分析

```bash
$SPARK_HOME/bin/spark-submit \
  --class CoffeeChainAnalysis \
  --master local[*] \
  --driver-memory 4g \
  --executor-memory 4g \
  CoffeeChainAnalysis.scala
```

## 输出结果

### 第一部分输出文件
- `peopleage.txt/` - 生成的人口年龄数据
- `average_age_result.txt/` - 平均年龄计算结果

### 第二部分输出文件
- `product_ranking/` - 产品销售排名
- `state_ranking/` - 州销售排名
- `sales_by_state/` - 按州销售分析
- `sales_by_market/` - 按市场销售分析
- `profit_by_product_type/` - 按产品类型利润分析
- `market_analysis/` - 市场规模分析
- `analysis_report/` - 综合分析报告

## 程序功能说明

### GeneratePeopleAge.scala
- 生成1000条模拟人口年龄数据
- 年龄范围：18-80岁
- 输出格式：序号\t年龄

### CalculateAverageAge.scala
- 读取人口年龄数据文件
- 计算平均年龄、最大最小年龄
- 统计年龄分布情况
- 生成详细分析报告

### CoffeeChainAnalysis.scala
- 数据预处理和清洗
- 多维度销售分析：
  - 按产品/地区销售排名
  - 销售量与州的关系
  - 销售量与市场的关系
  - 利润与售价关系分析
  - 市场规模与销售量关系

## 技术特点

1. **数据处理**：
   - 使用Spark RDD和DataFrame API
   - 支持大规模数据处理
   - 内存计算优化

2. **数据分析**：
   - 多维度统计分析
   - 相关性分析
   - 数据聚合和排序

3. **结果输出**：
   - CSV格式结构化输出
   - 文本格式分析报告
   - 支持后续可视化处理

## 故障排除

### 常见问题

1. **内存不足**：
   - 增加driver和executor内存配置
   - 使用`--driver-memory 4g --executor-memory 4g`

2. **文件路径问题**：
   - 确保CoffeeChain.csv在当前目录
   - 检查文件读写权限

3. **Spark环境问题**：
   - 检查SPARK_HOME环境变量
   - 确认Spark服务正常运行

### 调试建议

- 查看Spark Web UI (通常在 http://localhost:4040)
- 检查日志输出中的错误信息
- 使用`--verbose`参数获取详细输出

## 联系信息

- 课程：《大数据实时处理技术》
- 学校：山东协和学院
- 年级：2022级大数据技术专业

## 许可证

本项目仅用于学术学习目的。
