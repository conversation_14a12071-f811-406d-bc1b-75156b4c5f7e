# 大数据实时处理任务使用说明

## 🎯 任务概述

本项目完成山东协和学院《大数据实时处理技术》期末考试的两个主要任务：
1. **RDD编程统计人口平均年龄**
2. **基于咖啡连锁店的Spark数据处理分析**

## 📁 文件清单

```
lbxx3/
├── 📄 核心程序文件
│   ├── GeneratePeopleAge.scala      # 生成人口年龄数据
│   ├── CalculateAverageAge.scala    # 计算平均年龄
│   ├── CoffeeChainAnalysis.scala    # 咖啡数据分析
│   └── test_simple.scala            # 简单测试程序
│
├── 🚀 运行脚本
│   ├── run_analysis.sh              # Linux主运行脚本
│   ├── run_analysis.bat             # Windows运行脚本
│   └── test_spark_shell.sh          # 环境测试脚本
│
├── 📚 文档资料
│   ├── README.md                    # 项目说明
│   ├── 运行指南.md                   # 详细运行指南
│   ├── 使用说明.md                   # 本文件
│   ├── 实训报告模版.md               # 完整实训报告
│   ├── 项目总结.md                   # 项目总结
│   └── 任务要求.md                   # 原始任务要求
│
└── 📊 数据文件
    └── CoffeeChain.csv              # 咖啡连锁店原始数据
```

## 🚀 快速开始

### 步骤1：环境检查
```bash
# 检查当前目录
pwd
# 应该显示：/home/<USER>/spark02

# 检查文件是否完整
ls -la *.scala *.sh *.csv
```

### 步骤2：测试环境
```bash
# 给测试脚本添加执行权限
chmod +x test_spark_shell.sh

# 运行环境测试
./test_spark_shell.sh
```

**如果测试成功，会显示：**
```
✓ Spark Shell环境测试成功！
```

### 步骤3：运行完整分析
```bash
# 给主脚本添加执行权限
chmod +x run_analysis.sh

# 运行完整分析程序
./run_analysis.sh
```

## 📋 运行方式对比

| 运行方式 | 优点 | 缺点 | 推荐度 |
|---------|------|------|--------|
| **自动脚本** | 一键运行，自动化程度高 | 出错时难以调试 | ⭐⭐⭐⭐⭐ |
| **手动spark-shell** | 可以逐步执行，便于调试 | 需要手动输入代码 | ⭐⭐⭐⭐ |
| **spark-submit** | 标准提交方式 | 需要编译，环境要求高 | ⭐⭐⭐ |

## 🔧 故障排除

### 常见问题及解决方案

#### 1. Java路径错误
**错误信息：** `/usr/lib/jvm/java-8-openjdk/bin/java: 没有那个文件或目录`

**解决方案：**
```bash
# 查找正确的Java路径
find /usr -name "java" -type f 2>/dev/null | grep bin

# 常见的Java路径
ls -la /usr/lib/jvm/
```

#### 2. Spark启动失败
**解决方案：**
```bash
# 检查Spark安装
ls -la /opt/spark/bin/

# 测试spark-shell
/opt/spark/bin/spark-shell --version
```

#### 3. 内存不足
**解决方案：**
```bash
# 减少内存配置
/opt/spark/bin/spark-shell --master local[*] --driver-memory 1g
```

#### 4. 权限问题
**解决方案：**
```bash
# 添加执行权限
chmod +x *.sh

# 检查文件权限
ls -la *.sh
```

## 📊 预期输出结果

### 第一部分：人口年龄统计
- ✅ `peopleage.txt/` - 1000条人口年龄数据
- ✅ `average_age_result.txt/` - 平均年龄分析报告

### 第二部分：咖啡数据分析
- ✅ `product_ranking/` - 产品销售排名
- ✅ `state_ranking/` - 州销售排名
- ✅ `sales_by_state/` - 按州销售分析
- ✅ `sales_by_market/` - 按市场销售分析
- ✅ `profit_by_product_type/` - 按产品类型利润分析
- ✅ `market_analysis/` - 市场规模分析
- ✅ `analysis_report/` - 综合分析报告

## 🎓 学习建议

### 对于初学者
1. **先运行测试脚本** - 确保环境正常
2. **使用自动脚本** - 快速看到结果
3. **查看输出文件** - 理解分析结果
4. **阅读代码** - 学习Spark编程

### 对于进阶学习
1. **手动运行代码** - 深入理解每个步骤
2. **修改参数** - 尝试不同的配置
3. **扩展功能** - 添加新的分析维度
4. **优化性能** - 学习Spark调优

## 📞 技术支持

### 自助解决
1. **查看日志** - 仔细阅读错误信息
2. **检查环境** - 确认Spark和Java配置
3. **参考文档** - 阅读`运行指南.md`
4. **逐步调试** - 使用手动运行方式

### 常用命令
```bash
# 查看Spark进程
jps | grep -i spark

# 查看端口占用
netstat -tlnp | grep 4040

# 清理临时文件
rm -rf spark-warehouse derby.log metastore_db

# 查看磁盘空间
df -h
```

## ✅ 验收标准

### 程序运行成功标志
1. **无错误退出** - 脚本返回码为0
2. **生成所有输出文件** - 检查文件列表
3. **数据结果合理** - 查看分析结果
4. **日志信息正常** - 无严重错误信息

### 结果验证方法
```bash
# 检查生成的文件
ls -la *ranking* *analysis* *result* peopleage*

# 查看文件内容示例
head -5 peopleage.txt/part-00000
head -5 product_ranking/part-00000
```

## 🎉 完成确认

当您看到以下输出时，说明任务已成功完成：

```
==========================================
所有任务执行完成！
==========================================
✓ 人口年龄数据生成成功
✓ 平均年龄计算成功  
✓ 咖啡连锁店数据分析成功
```

恭喜您成功完成了大数据实时处理技术期末考试任务！

---

**项目作者**：学生姓名  
**完成时间**：2024年12月  
**课程**：《大数据实时处理技术》  
**学校**：山东协和学院
