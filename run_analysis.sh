#!/bin/bash

# 大数据实时处理任务运行脚本
# 作者：学生姓名
# 日期：$(date)

echo "=========================================="
echo "大数据实时处理技术期末考试任务"
echo "=========================================="

# 设置环境变量
export SPARK_HOME=/opt/spark  # 根据实际Spark安装路径调整
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk  # 根据实际Java安装路径调整

# 检查Spark环境
if [ ! -d "$SPARK_HOME" ]; then
    echo "错误：未找到Spark安装目录，请检查SPARK_HOME环境变量"
    exit 1
fi

echo "Spark安装路径：$SPARK_HOME"
echo "Java安装路径：$JAVA_HOME"
echo ""

# 第一部分：RDD编程统计人口平均年龄
echo "第一部分：RDD编程统计人口平均年龄"
echo "=========================================="

echo "1. 生成模拟人口年龄数据..."
$SPARK_HOME/bin/spark-submit \
    --class GeneratePeopleAge \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 2g \
    GeneratePeopleAge.scala

if [ $? -eq 0 ]; then
    echo "✓ 人口年龄数据生成成功"
else
    echo "✗ 人口年龄数据生成失败"
    exit 1
fi

echo ""
echo "2. 计算平均年龄..."
$SPARK_HOME/bin/spark-submit \
    --class CalculateAverageAge \
    --master local[*] \
    --driver-memory 2g \
    --executor-memory 2g \
    CalculateAverageAge.scala

if [ $? -eq 0 ]; then
    echo "✓ 平均年龄计算成功"
else
    echo "✗ 平均年龄计算失败"
    exit 1
fi

echo ""

# 第二部分：基于咖啡连锁店的Spark数据处理分析
echo "第二部分：基于咖啡连锁店的Spark数据处理分析"
echo "=========================================="

# 检查数据文件是否存在
if [ ! -f "CoffeeChain.csv" ]; then
    echo "错误：未找到CoffeeChain.csv数据文件"
    exit 1
fi

echo "3. 执行咖啡连锁店数据分析..."
$SPARK_HOME/bin/spark-submit \
    --class CoffeeChainAnalysis \
    --master local[*] \
    --driver-memory 4g \
    --executor-memory 4g \
    --conf spark.sql.adaptive.enabled=true \
    --conf spark.sql.adaptive.coalescePartitions.enabled=true \
    CoffeeChainAnalysis.scala

if [ $? -eq 0 ]; then
    echo "✓ 咖啡连锁店数据分析成功"
else
    echo "✗ 咖啡连锁店数据分析失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "所有任务执行完成！"
echo "=========================================="

# 显示生成的文件和目录
echo "生成的文件和目录："
echo "1. 人口年龄数据："
ls -la peopleage.txt* 2>/dev/null || echo "   未找到peopleage.txt文件"
ls -la average_age_result.txt* 2>/dev/null || echo "   未找到average_age_result.txt文件"

echo ""
echo "2. 咖啡分析结果："
ls -la product_ranking* 2>/dev/null || echo "   未找到product_ranking目录"
ls -la state_ranking* 2>/dev/null || echo "   未找到state_ranking目录"
ls -la sales_by_state* 2>/dev/null || echo "   未找到sales_by_state目录"
ls -la sales_by_market* 2>/dev/null || echo "   未找到sales_by_market目录"
ls -la profit_by_product_type* 2>/dev/null || echo "   未找到profit_by_product_type目录"
ls -la market_analysis* 2>/dev/null || echo "   未找到market_analysis目录"
ls -la analysis_report* 2>/dev/null || echo "   未找到analysis_report目录"

echo ""
echo "任务完成时间：$(date)"
echo "请查看生成的结果文件进行验证。"
