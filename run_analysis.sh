#!/bin/bash

# 大数据实时处理任务运行脚本 (使用spark-shell)
# 作者：学生姓名
# 日期：$(date)

echo "=========================================="
echo "大数据实时处理技术期末考试任务"
echo "=========================================="

# 设置环境变量 - 自动检测Java路径
export SPARK_HOME=/opt/spark

# 自动查找Java安装路径
if [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ]; then
    export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
elif [ -d "/usr/lib/jvm/java-1.8.0-openjdk" ]; then
    export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
elif [ -d "/usr/lib/jvm/java-8-oracle" ]; then
    export JAVA_HOME=/usr/lib/jvm/java-8-oracle
else
    # 尝试使用系统默认Java
    JAVA_HOME=$(dirname $(dirname $(readlink -f $(which java))))
fi

# 检查Spark环境
if [ ! -d "$SPARK_HOME" ]; then
    echo "错误：未找到Spark安装目录，请检查SPARK_HOME环境变量"
    exit 1
fi

# 检查Java环境
if [ ! -f "$JAVA_HOME/bin/java" ]; then
    echo "警告：Java路径可能不正确，尝试使用系统默认Java"
    export JAVA_HOME=""
fi

echo "Spark安装路径：$SPARK_HOME"
echo "Java安装路径：$JAVA_HOME"
echo ""

# 第一部分：RDD编程统计人口平均年龄
echo "第一部分：RDD编程统计人口平均年龄"
echo "=========================================="

echo "1. 生成模拟人口年龄数据..."
echo "正在启动spark-shell执行GeneratePeopleAge程序..."

$SPARK_HOME/bin/spark-shell --master local[*] --driver-memory 2g << 'EOF'
import scala.util.Random

println("开始生成人口年龄数据...")

// 配置参数
val numRecords = 1000  // 生成1000条记录
val outputPath = "peopleage.txt"  // 输出文件路径

// 生成随机年龄数据（18-80岁之间）
val random = new Random()
val ageData = (1 to numRecords).map { id =>
  val age = 18 + random.nextInt(63)  // 生成18-80岁之间的随机年龄
  s"$id\t$age"  // 格式：序号\t年龄
}

// 将数据转换为RDD
val ageRDD = sc.parallelize(ageData)

// 保存到本地文件系统
ageRDD.coalesce(1).saveAsTextFile(outputPath)

println(s"数据生成完成！")
println(s"文件保存路径：$outputPath")
println(s"记录总数：$numRecords")

// 显示前10条数据作为示例
println("\n前10条数据示例：")
ageRDD.take(10).foreach(println)

// 统计年龄分布
val ageStats = ageRDD.map(line => {
  val parts = line.split("\t")
  parts(1).toInt
})

val minAge = ageStats.min()
val maxAge = ageStats.max()
val avgAge = ageStats.mean()

println(s"\n数据统计信息：")
println(s"最小年龄：$minAge")
println(s"最大年龄：$maxAge")
println(s"平均年龄：${avgAge.formatted("%.2f")}")

println("\n程序执行完成！")
System.exit(0)
EOF

if [ $? -eq 0 ]; then
    echo "✓ 人口年龄数据生成成功"
else
    echo "✗ 人口年龄数据生成失败"
    exit 1
fi

echo ""
echo "2. 计算平均年龄..."
echo "正在启动spark-shell执行CalculateAverageAge程序..."

$SPARK_HOME/bin/spark-shell --master local[*] --driver-memory 2g << 'EOF'
println("开始计算人口平均年龄...")

// 输入文件路径
val inputPath = "peopleage.txt"

try {
  // 读取数据文件
  val dataRDD = sc.textFile(inputPath)

  // 检查数据是否为空
  if (dataRDD.isEmpty()) {
    println("错误：数据文件为空或不存在！")
    System.exit(1)
  }

  println(s"成功读取数据文件：$inputPath")

  // 解析数据，提取年龄信息
  val ageRDD = dataRDD.map(line => {
    val parts = line.split("\t")
    if (parts.length >= 2) {
      parts(1).toInt  // 第二列是年龄
    } else {
      throw new IllegalArgumentException(s"数据格式错误：$line")
    }
  })

  // 缓存RDD，因为会多次使用
  ageRDD.cache()

  // 统计基本信息
  val totalCount = ageRDD.count()
  val totalAge = ageRDD.reduce(_ + _)
  val averageAge = totalAge.toDouble / totalCount

  // 计算其他统计信息
  val minAge = ageRDD.min()
  val maxAge = ageRDD.max()

  // 计算年龄分布
  val ageGroups = ageRDD.map(age => {
    age match {
      case a if a < 20 => "18-19岁"
      case a if a < 30 => "20-29岁"
      case a if a < 40 => "30-39岁"
      case a if a < 50 => "40-49岁"
      case a if a < 60 => "50-59岁"
      case a if a < 70 => "60-69岁"
      case _ => "70岁以上"
    }
  }).countByValue()

  // 输出结果
  println("\n" + "="*50)
  println("人口年龄统计分析结果")
  println("="*50)
  println(s"总人口数量：$totalCount")
  println(s"年龄总和：$totalAge")
  println(s"平均年龄：${averageAge.formatted("%.2f")}岁")
  println(s"最小年龄：$minAge岁")
  println(s"最大年龄：$maxAge岁")

  println("\n年龄分布统计：")
  ageGroups.toSeq.sortBy(_._1).foreach { case (group, count) =>
    val percentage = (count.toDouble / totalCount * 100).formatted("%.1f")
    println(s"$group: $count人 ($percentage%)")
  }

  // 显示前10条原始数据
  println("\n前10条原始数据：")
  dataRDD.take(10).foreach(println)

  // 保存结果到文件
  val resultPath = "average_age_result.txt"
  val resultData = Seq(
    s"人口年龄统计分析结果",
    s"分析时间：${java.time.LocalDateTime.now()}",
    s"总人口数量：$totalCount",
    s"平均年龄：${averageAge.formatted("%.2f")}岁",
    s"最小年龄：$minAge岁",
    s"最大年龄：$maxAge岁",
    s"年龄分布：",
    ageGroups.toSeq.sortBy(_._1).map { case (group, count) =>
      val percentage = (count.toDouble / totalCount * 100).formatted("%.1f")
      s"  $group: $count人 ($percentage%)"
    }.mkString("\n")
  )

  sc.parallelize(resultData).coalesce(1).saveAsTextFile(resultPath)
  println(s"\n分析结果已保存到：$resultPath")

} catch {
  case e: Exception =>
    println(s"处理数据时发生错误：${e.getMessage}")
    e.printStackTrace()
    System.exit(1)
}

println("\n程序执行完成！")
System.exit(0)
EOF

if [ $? -eq 0 ]; then
    echo "✓ 平均年龄计算成功"
else
    echo "✗ 平均年龄计算失败"
    exit 1
fi

echo ""

# 第二部分：基于咖啡连锁店的Spark数据处理分析
echo "第二部分：基于咖啡连锁店的Spark数据处理分析"
echo "=========================================="

# 检查数据文件是否存在
if [ ! -f "CoffeeChain.csv" ]; then
    echo "错误：未找到CoffeeChain.csv数据文件"
    exit 1
fi

echo "3. 执行咖啡连锁店数据分析..."
echo "正在启动spark-shell执行CoffeeChainAnalysis程序..."

$SPARK_HOME/bin/spark-shell --master local[*] --driver-memory 4g << 'EOF'
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

println("开始咖啡连锁店数据分析...")

try {
  // 1. 数据预处理 - 读取和清洗数据
  println("\n1. 数据预处理...")

  val inputPath = "CoffeeChain.csv"

  // 读取CSV文件
  val rawDF = spark.read
    .option("header", "true")
    .option("inferSchema", "true")
    .csv(inputPath)

  // 数据清洗：处理Inventory字段中的逗号
  val cleanDF = rawDF.withColumn("Inventory_Clean",
    regexp_replace(col("Inventory"), ",", "").cast(DoubleType))
    .drop("Inventory")
    .withColumnRenamed("Inventory_Clean", "Inventory")

  // 过滤掉空值和异常数据
  val coffeeDF = cleanDF.filter(
    col("Coffee Sales").isNotNull &&
    col("Coffee Sales") > 0 &&
    col("State").isNotNull &&
    col("Market").isNotNull
  )

  // 缓存数据框，因为会多次使用
  coffeeDF.cache()

  val totalRecords = coffeeDF.count()
  println(s"数据预处理完成，有效记录数：$totalRecords")

  // 显示数据概览
  println("\n数据概览：")
  coffeeDF.show(5)

  // 2. 查看咖啡销售量排名并存储
  println("\n2. 咖啡销售量排名分析...")

  // 按产品排名
  val productRanking = coffeeDF
    .groupBy("Product", "Product Type")
    .agg(
      sum("Coffee Sales").alias("Total_Sales"),
      avg("Coffee Sales").alias("Avg_Sales"),
      count("*").alias("Record_Count")
    )
    .orderBy(desc("Total_Sales"))

  println("按产品销售量排名（前10名）：")
  productRanking.show(10)

  // 按地区排名
  val stateRanking = coffeeDF
    .groupBy("State", "Market")
    .agg(
      sum("Coffee Sales").alias("Total_Sales"),
      avg("Coffee Sales").alias("Avg_Sales"),
      count("*").alias("Record_Count")
    )
    .orderBy(desc("Total_Sales"))

  println("按州销售量排名：")
  stateRanking.show()

  // 保存排名结果
  productRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("product_ranking")
  stateRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("state_ranking")

  println("排名结果已保存到 product_ranking 和 state_ranking 目录")

  // 3. 销售分布分析
  println("\n3. 销售分布分析...")

  // 3.1 咖啡销售量和State的关系
  println("\n3.1 咖啡销售量和State的关系：")
  val salesByState = coffeeDF
    .groupBy("State")
    .agg(
      sum("Coffee Sales").alias("Total_Sales"),
      avg("Coffee Sales").alias("Avg_Sales"),
      min("Coffee Sales").alias("Min_Sales"),
      max("Coffee Sales").alias("Max_Sales"),
      count("*").alias("Record_Count")
    )
    .orderBy(desc("Total_Sales"))

  salesByState.show()

  // 3.2 咖啡销售量和Market的关系
  println("\n3.2 咖啡销售量和Market的关系：")
  val salesByMarket = coffeeDF
    .groupBy("Market", "Market Size")
    .agg(
      sum("Coffee Sales").alias("Total_Sales"),
      avg("Coffee Sales").alias("Avg_Sales"),
      count("*").alias("Record_Count")
    )
    .orderBy(desc("Total_Sales"))

  salesByMarket.show()

  // 3.3 咖啡的平均利润和售价
  println("\n3.3 咖啡的平均利润和售价：")
  val profitAndSales = coffeeDF
    .agg(
      avg("Profit").alias("Avg_Profit"),
      avg("Coffee Sales").alias("Avg_Sales"),
      avg("Margin").alias("Avg_Margin"),
      sum("Profit").alias("Total_Profit"),
      sum("Coffee Sales").alias("Total_Sales")
    )

  profitAndSales.show()

  // 3.4 按产品类型分析平均利润和售价
  println("\n3.4 按产品类型分析平均利润和售价：")
  val profitByProductType = coffeeDF
    .groupBy("Product Type")
    .agg(
      avg("Profit").alias("Avg_Profit"),
      avg("Coffee Sales").alias("Avg_Sales"),
      avg("Margin").alias("Avg_Margin"),
      sum("Profit").alias("Total_Profit"),
      sum("Coffee Sales").alias("Total_Sales"),
      count("*").alias("Record_Count")
    )
    .orderBy(desc("Avg_Profit"))

  profitByProductType.show()

  // 3.5 咖啡的平均利润、销售量与其他成本的关系
  println("\n3.5 咖啡的平均利润、销售量与其他成本的关系：")
  val costAnalysis = coffeeDF
    .agg(
      avg("Profit").alias("Avg_Profit"),
      avg("Coffee Sales").alias("Avg_Sales"),
      avg("Cogs").alias("Avg_Cogs"),
      avg("Marketing").alias("Avg_Marketing"),
      avg("Total Expenses").alias("Avg_Total_Expenses"),
      corr("Profit", "Coffee Sales").alias("Profit_Sales_Correlation"),
      corr("Coffee Sales", "Marketing").alias("Sales_Marketing_Correlation"),
      corr("Profit", "Total Expenses").alias("Profit_Expenses_Correlation")
    )

  costAnalysis.show()

  // 3.6 市场规模、市场地域与销售量的关系
  println("\n3.6 市场规模、市场地域与销售量的关系：")
  val marketAnalysis = coffeeDF
    .groupBy("Market Size", "Market")
    .agg(
      sum("Coffee Sales").alias("Total_Sales"),
      avg("Coffee Sales").alias("Avg_Sales"),
      avg("Profit").alias("Avg_Profit"),
      count("*").alias("Record_Count")
    )
    .orderBy(desc("Total_Sales"))

  marketAnalysis.show()

  // 保存所有分析结果
  salesByState.coalesce(1).write.mode("overwrite").option("header", "true").csv("sales_by_state")
  salesByMarket.coalesce(1).write.mode("overwrite").option("header", "true").csv("sales_by_market")
  profitByProductType.coalesce(1).write.mode("overwrite").option("header", "true").csv("profit_by_product_type")
  marketAnalysis.coalesce(1).write.mode("overwrite").option("header", "true").csv("market_analysis")

  // 生成综合分析报告
  val reportData = Seq(
    "咖啡连锁店数据分析报告",
    s"分析时间：${java.time.LocalDateTime.now()}",
    s"数据记录总数：$totalRecords",
    "",
    "主要发现：",
    s"1. 总销售额最高的州：${salesByState.first().getString(0)}",
    s"2. 平均利润最高的产品类型：${profitByProductType.first().getString(0)}",
    s"3. 销售量最大的市场：${salesByMarket.first().getString(0)}",
    "",
    "详细分析结果已保存到以下目录：",
    "- product_ranking: 产品销售排名",
    "- state_ranking: 州销售排名",
    "- sales_by_state: 按州销售分析",
    "- sales_by_market: 按市场销售分析",
    "- profit_by_product_type: 按产品类型利润分析",
    "- market_analysis: 市场规模分析"
  )

  sc.parallelize(reportData).coalesce(1).saveAsTextFile("analysis_report")

  println("\n所有分析结果已保存完成！")

} catch {
  case e: Exception =>
    println(s"数据处理过程中发生错误：${e.getMessage}")
    e.printStackTrace()
    System.exit(1)
}

println("\n程序执行完成！")
System.exit(0)
EOF

if [ $? -eq 0 ]; then
    echo "✓ 咖啡连锁店数据分析成功"
else
    echo "✗ 咖啡连锁店数据分析失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "所有任务执行完成！"
echo "=========================================="

# 显示生成的文件和目录
echo "生成的文件和目录："
echo "1. 人口年龄数据："
ls -la peopleage.txt* 2>/dev/null || echo "   未找到peopleage.txt文件"
ls -la average_age_result.txt* 2>/dev/null || echo "   未找到average_age_result.txt文件"

echo ""
echo "2. 咖啡分析结果："
ls -la product_ranking* 2>/dev/null || echo "   未找到product_ranking目录"
ls -la state_ranking* 2>/dev/null || echo "   未找到state_ranking目录"
ls -la sales_by_state* 2>/dev/null || echo "   未找到sales_by_state目录"
ls -la sales_by_market* 2>/dev/null || echo "   未找到sales_by_market目录"
ls -la profit_by_product_type* 2>/dev/null || echo "   未找到profit_by_product_type目录"
ls -la market_analysis* 2>/dev/null || echo "   未找到market_analysis目录"
ls -la analysis_report* 2>/dev/null || echo "   未找到analysis_report目录"

echo ""
echo "任务完成时间：$(date)"
echo "请查看生成的结果文件进行验证。"
