#!/bin/bash

# 测试spark-shell环境脚本
echo "=========================================="
echo "测试Spark Shell环境"
echo "=========================================="

# 设置环境变量
export SPARK_HOME=/opt/spark

# 自动查找Java安装路径
if [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ]; then
    export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
elif [ -d "/usr/lib/jvm/java-1.8.0-openjdk" ]; then
    export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
elif [ -d "/usr/lib/jvm/java-8-oracle" ]; then
    export JAVA_HOME=/usr/lib/jvm/java-8-oracle
else
    # 尝试使用系统默认Java
    JAVA_HOME=$(dirname $(dirname $(readlink -f $(which java))))
fi

echo "Spark安装路径：$SPARK_HOME"
echo "Java安装路径：$JAVA_HOME"
echo ""

echo "开始测试基本Spark功能..."

$SPARK_HOME/bin/spark-shell --master local[*] --driver-memory 1g << 'EOF'
println("=== Spark Shell 环境测试 ===")

// 测试1：基本RDD操作
println("\n测试1：基本RDD操作")
val numbers = sc.parallelize(1 to 10)
val sum = numbers.reduce(_ + _)
println(s"1到10的和：$sum")

// 测试2：简单数据处理
println("\n测试2：简单数据处理")
val testData = Seq("1\t25", "2\t30", "3\t35", "4\t40", "5\t45")
val testRDD = sc.parallelize(testData)

val ages = testRDD.map(line => {
  val parts = line.split("\t")
  parts(1).toInt
})

val avgAge = ages.mean()
println(s"平均年龄：${avgAge.formatted("%.2f")}")

// 测试3：DataFrame操作
println("\n测试3：DataFrame操作")
import spark.implicits._

case class Person(id: Int, age: Int)
val persons = Seq(Person(1, 25), Person(2, 30), Person(3, 35))
val df = spark.createDataFrame(persons)

println("DataFrame内容：")
df.show()

val avgAgeDF = df.agg(avg("age").alias("avg_age"))
println("DataFrame计算的平均年龄：")
avgAgeDF.show()

// 测试4：文件操作
println("\n测试4：文件操作")
val testResult = Seq(
  "Spark环境测试结果",
  s"测试时间：${java.time.LocalDateTime.now()}",
  s"基本RDD操作：成功",
  s"数据处理：成功",
  s"DataFrame操作：成功",
  s"计算结果：1到10的和=$sum，平均年龄=${avgAge.formatted("%.2f")}",
  "所有测试通过！"
)

sc.parallelize(testResult).coalesce(1).saveAsTextFile("spark_test_result")
println("测试结果已保存到 spark_test_result 目录")

println("\n=== 所有测试完成！===")
System.exit(0)
EOF

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Spark Shell环境测试成功！"
    echo ""
    echo "生成的测试文件："
    ls -la spark_test_result* 2>/dev/null || echo "未找到测试结果文件"
    echo ""
    echo "现在可以运行完整的分析脚本："
    echo "./run_analysis.sh"
else
    echo ""
    echo "✗ Spark Shell环境测试失败！"
    echo "请检查Spark和Java环境配置"
    exit 1
fi
