import org.apache.spark.sql.SparkSession

/**
 * 计算人口平均年龄
 * 功能：读取本地文件系统中的数据文件peopleage.txt，计算所有人口的平均年龄
 */
object CalculateAverageAge {
  
  def main(args: Array[String]): Unit = {
    
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CalculateAverageAge")
      .master("local[*]")  // 本地模式运行
      .getOrCreate()
    
    val sc = spark.sparkContext
    
    // 设置日志级别为WARN，减少输出信息
    sc.setLogLevel("WARN")
    
    println("开始计算人口平均年龄...")
    
    // 输入文件路径
    val inputPath = "peopleage.txt"
    
    try {
      // 读取数据文件
      val dataRDD = sc.textFile(inputPath)
      
      // 检查数据是否为空
      if (dataRDD.isEmpty()) {
        println("错误：数据文件为空或不存在！")
        return
      }
      
      println(s"成功读取数据文件：$inputPath")
      
      // 解析数据，提取年龄信息
      val ageRDD = dataRDD.map(line => {
        val parts = line.split("\t")
        if (parts.length >= 2) {
          parts(1).toInt  // 第二列是年龄
        } else {
          throw new IllegalArgumentException(s"数据格式错误：$line")
        }
      })
      
      // 缓存RDD，因为会多次使用
      ageRDD.cache()
      
      // 统计基本信息
      val totalCount = ageRDD.count()
      val totalAge = ageRDD.reduce(_ + _)
      val averageAge = totalAge.toDouble / totalCount
      
      // 计算其他统计信息
      val minAge = ageRDD.min()
      val maxAge = ageRDD.max()
      
      // 计算年龄分布
      val ageGroups = ageRDD.map(age => {
        age match {
          case a if a < 20 => "18-19岁"
          case a if a < 30 => "20-29岁"
          case a if a < 40 => "30-39岁"
          case a if a < 50 => "40-49岁"
          case a if a < 60 => "50-59岁"
          case a if a < 70 => "60-69岁"
          case _ => "70岁以上"
        }
      }).countByValue()
      
      // 输出结果
      println("\n" + "="*50)
      println("人口年龄统计分析结果")
      println("="*50)
      println(s"总人口数量：$totalCount")
      println(s"年龄总和：$totalAge")
      println(s"平均年龄：${averageAge.formatted("%.2f")}岁")
      println(s"最小年龄：$minAge岁")
      println(s"最大年龄：$maxAge岁")
      
      println("\n年龄分布统计：")
      ageGroups.toSeq.sortBy(_._1).foreach { case (group, count) =>
        val percentage = (count.toDouble / totalCount * 100).formatted("%.1f")
        println(s"$group: $count人 ($percentage%)")
      }
      
      // 显示前10条原始数据
      println("\n前10条原始数据：")
      dataRDD.take(10).foreach(println)
      
      // 保存结果到文件
      val resultPath = "average_age_result.txt"
      val resultData = Seq(
        s"人口年龄统计分析结果",
        s"分析时间：${java.time.LocalDateTime.now()}",
        s"总人口数量：$totalCount",
        s"平均年龄：${averageAge.formatted("%.2f")}岁",
        s"最小年龄：$minAge岁",
        s"最大年龄：$maxAge岁",
        s"年龄分布：",
        ageGroups.toSeq.sortBy(_._1).map { case (group, count) =>
          val percentage = (count.toDouble / totalCount * 100).formatted("%.1f")
          s"  $group: $count人 ($percentage%)"
        }.mkString("\n")
      )
      
      sc.parallelize(resultData).coalesce(1).saveAsTextFile(resultPath)
      println(s"\n分析结果已保存到：$resultPath")
      
    } catch {
      case e: Exception =>
        println(s"处理数据时发生错误：${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 关闭SparkSession
      spark.stop()
      println("\n程序执行完成！")
    }
  }
}
