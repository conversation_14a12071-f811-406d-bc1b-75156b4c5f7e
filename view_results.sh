#!/bin/bash

# 大数据实时处理任务结果查看脚本
# 作者：学生姓名
# 功能：查看所有分析结果的摘要和详细信息

echo "=========================================="
echo "大数据实时处理任务结果查看"
echo "=========================================="

# 检查当前目录
echo "当前工作目录：$(pwd)"
echo "检查时间：$(date)"
echo ""

# 第一部分：人口年龄统计结果
echo "【第一部分：人口年龄统计结果】"
echo "----------------------------------------"

if [ -f "peopleage.txt/part-00000" ]; then
    record_count=$(wc -l < peopleage.txt/part-00000)
    echo "✓ 人口年龄数据：$record_count 条记录"
    echo "  文件路径：peopleage.txt/part-00000"
    echo "  数据示例（前5行）："
    head -5 peopleage.txt/part-00000 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到人口年龄数据文件 (peopleage.txt/part-00000)"
fi

if [ -f "average_age_result.txt/part-00000" ]; then
    echo "✓ 平均年龄分析报告已生成"
    echo "  文件路径：average_age_result.txt/part-00000"
    echo "  报告内容："
    cat average_age_result.txt/part-00000 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到平均年龄分析报告 (average_age_result.txt/part-00000)"
fi

# 第二部分：咖啡连锁店数据分析结果
echo "【第二部分：咖啡连锁店数据分析结果】"
echo "----------------------------------------"

# 1. 产品销售排名
if [ -f "product_ranking/part-00000" ]; then
    record_count=$(wc -l < product_ranking/part-00000)
    echo "✓ 产品销售排名：$record_count 条记录"
    echo "  文件路径：product_ranking/part-00000"
    echo "  销量前5名产品："
    head -6 product_ranking/part-00000 | tail -5 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到产品销售排名文件 (product_ranking/part-00000)"
fi

# 2. 州销售排名
if [ -f "state_ranking/part-00000" ]; then
    record_count=$(wc -l < state_ranking/part-00000)
    echo "✓ 州销售排名：$record_count 条记录"
    echo "  文件路径：state_ranking/part-00000"
    echo "  销量前5名州："
    head -6 state_ranking/part-00000 | tail -5 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到州销售排名文件 (state_ranking/part-00000)"
fi

# 3. 按州销售分析
if [ -f "sales_by_state/part-00000" ]; then
    record_count=$(wc -l < sales_by_state/part-00000)
    echo "✓ 按州销售分析：$record_count 条记录"
    echo "  文件路径：sales_by_state/part-00000"
    echo "  前3名州的详细数据："
    head -4 sales_by_state/part-00000 | tail -3 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到按州销售分析文件 (sales_by_state/part-00000)"
fi

# 4. 按市场销售分析
if [ -f "sales_by_market/part-00000" ]; then
    record_count=$(wc -l < sales_by_market/part-00000)
    echo "✓ 按市场销售分析：$record_count 条记录"
    echo "  文件路径：sales_by_market/part-00000"
    echo "  市场销售数据："
    head -4 sales_by_market/part-00000 | tail -3 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到按市场销售分析文件 (sales_by_market/part-00000)"
fi

# 5. 按产品类型利润分析
if [ -f "profit_by_product_type/part-00000" ]; then
    record_count=$(wc -l < profit_by_product_type/part-00000)
    echo "✓ 按产品类型利润分析：$record_count 条记录"
    echo "  文件路径：profit_by_product_type/part-00000"
    echo "  利润前3名产品类型："
    head -4 profit_by_product_type/part-00000 | tail -3 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到按产品类型利润分析文件 (profit_by_product_type/part-00000)"
fi

# 6. 市场规模分析
if [ -f "market_analysis/part-00000" ]; then
    record_count=$(wc -l < market_analysis/part-00000)
    echo "✓ 市场规模分析：$record_count 条记录"
    echo "  文件路径：market_analysis/part-00000"
    echo "  市场规模数据："
    head -4 market_analysis/part-00000 | tail -3 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到市场规模分析文件 (market_analysis/part-00000)"
fi

# 7. 综合分析报告
if [ -f "analysis_report/part-00000" ]; then
    echo "✓ 综合分析报告已生成"
    echo "  文件路径：analysis_report/part-00000"
    echo "  报告内容："
    cat analysis_report/part-00000 | sed 's/^/    /'
    echo ""
else
    echo "✗ 未找到综合分析报告 (analysis_report/part-00000)"
fi

# 文件大小统计
echo "【文件大小统计】"
echo "----------------------------------------"
echo "生成的所有文件和目录大小："
du -sh peopleage.txt* average_age_result.txt* *ranking* *analysis* sales_by* profit_by* market_analysis* 2>/dev/null | sed 's/^/  /'

echo ""
echo "【所有生成的目录列表】"
echo "----------------------------------------"
ls -la | grep -E "(peopleage|average_age|ranking|analysis|sales_by|profit_by|market_analysis)" | sed 's/^/  /'

echo ""
echo "=========================================="
echo "结果查看完成！"
echo "=========================================="

echo ""
echo "💡 提示："
echo "  - 要查看详细内容，使用：cat 文件路径"
echo "  - 要格式化显示CSV，使用：column -t -s',' 文件路径"
echo "  - 要查看特定行数，使用：head -n 行数 文件路径"
echo "  - 要搜索内容，使用：grep '关键词' 文件路径"
