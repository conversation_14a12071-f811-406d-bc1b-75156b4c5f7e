import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

/**
 * 基于咖啡连锁店的Spark数据处理分析
 * 功能：对CoffeeChain.csv数据进行多维度分析
 */
object CoffeeChainAnalysis {
  
  def main(args: Array[String]): Unit = {
    
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("CoffeeChainAnalysis")
      .master("local[*]")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .getOrCreate()
    
    import spark.implicits._
    
    // 设置日志级别
    spark.sparkContext.setLogLevel("WARN")
    
    println("开始咖啡连锁店数据分析...")
    
    try {
      // 1. 数据预处理 - 读取和清洗数据
      println("\n1. 数据预处理...")
      
      val inputPath = "CoffeeChain.csv"
      
      // 定义数据模式
      val schema = StructType(Array(
        StructField("Area_Code", StringType, true),
        StructField("Date", StringType, true),
        StructField("Market", StringType, true),
        StructField("Market_Size", StringType, true),
        StructField("Product", StringType, true),
        StructField("Product_Type", StringType, true),
        StructField("State", StringType, true),
        StructField("Type", StringType, true),
        StructField("Budget_Cogs", DoubleType, true),
        StructField("Budget_Margin", DoubleType, true),
        StructField("Budget_Profit", DoubleType, true),
        StructField("Budget_Sales", DoubleType, true),
        StructField("Coffee_Sales", DoubleType, true),
        StructField("Cogs", DoubleType, true),
        StructField("Inventory", StringType, true),  // 可能包含逗号的数字
        StructField("Margin", DoubleType, true),
        StructField("Marketing", DoubleType, true),
        StructField("Number_of_Records", IntegerType, true),
        StructField("Profit", DoubleType, true),
        StructField("Total_Expenses", DoubleType, true)
      ))
      
      // 读取CSV文件
      val rawDF = spark.read
        .option("header", "true")
        .option("inferSchema", "false")
        .schema(schema)
        .csv(inputPath)
      
      // 数据清洗：处理Inventory字段中的逗号
      val cleanDF = rawDF.withColumn("Inventory_Clean", 
        regexp_replace(col("Inventory"), ",", "").cast(DoubleType))
        .drop("Inventory")
        .withColumnRenamed("Inventory_Clean", "Inventory")
      
      // 过滤掉空值和异常数据
      val coffeeDF = cleanDF.filter(
        col("Coffee_Sales").isNotNull && 
        col("Coffee_Sales") > 0 &&
        col("State").isNotNull &&
        col("Market").isNotNull
      )
      
      // 缓存数据框，因为会多次使用
      coffeeDF.cache()
      
      val totalRecords = coffeeDF.count()
      println(s"数据预处理完成，有效记录数：$totalRecords")
      
      // 显示数据概览
      println("\n数据概览：")
      coffeeDF.show(5)
      
      // 2. 查看咖啡销售量排名并存储
      println("\n2. 咖啡销售量排名分析...")
      
      // 按产品排名
      val productRanking = coffeeDF
        .groupBy("Product", "Product_Type")
        .agg(
          sum("Coffee_Sales").alias("Total_Sales"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count")
        )
        .orderBy(desc("Total_Sales"))
      
      println("按产品销售量排名（前10名）：")
      productRanking.show(10)
      
      // 按地区排名
      val stateRanking = coffeeDF
        .groupBy("State", "Market")
        .agg(
          sum("Coffee_Sales").alias("Total_Sales"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count")
        )
        .orderBy(desc("Total_Sales"))
      
      println("按州销售量排名：")
      stateRanking.show()
      
      // 保存排名结果
      productRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("product_ranking")
      stateRanking.coalesce(1).write.mode("overwrite").option("header", "true").csv("state_ranking")

      println("排名结果已保存到 product_ranking 和 state_ranking 目录")

      // 3. 销售分布分析
      println("\n3. 销售分布分析...")

      // 3.1 咖啡销售量和State的关系
      println("\n3.1 咖啡销售量和State的关系：")
      val salesByState = coffeeDF
        .groupBy("State")
        .agg(
          sum("Coffee_Sales").alias("Total_Sales"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          min("Coffee_Sales").alias("Min_Sales"),
          max("Coffee_Sales").alias("Max_Sales"),
          count("*").alias("Record_Count")
        )
        .orderBy(desc("Total_Sales"))

      salesByState.show()

      // 3.2 咖啡销售量和Market的关系
      println("\n3.2 咖啡销售量和Market的关系：")
      val salesByMarket = coffeeDF
        .groupBy("Market", "Market_Size")
        .agg(
          sum("Coffee_Sales").alias("Total_Sales"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          count("*").alias("Record_Count")
        )
        .orderBy(desc("Total_Sales"))

      salesByMarket.show()

      // 3.3 咖啡的平均利润和售价
      println("\n3.3 咖啡的平均利润和售价：")
      val profitAndSales = coffeeDF
        .agg(
          avg("Profit").alias("Avg_Profit"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          avg("Margin").alias("Avg_Margin"),
          sum("Profit").alias("Total_Profit"),
          sum("Coffee_Sales").alias("Total_Sales")
        )

      profitAndSales.show()

      // 3.4 按产品类型分析平均利润和售价
      println("\n3.4 按产品类型分析平均利润和售价：")
      val profitByProductType = coffeeDF
        .groupBy("Product_Type")
        .agg(
          avg("Profit").alias("Avg_Profit"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          avg("Margin").alias("Avg_Margin"),
          sum("Profit").alias("Total_Profit"),
          sum("Coffee_Sales").alias("Total_Sales"),
          count("*").alias("Record_Count")
        )
        .orderBy(desc("Avg_Profit"))

      profitByProductType.show()

      // 3.5 咖啡的平均利润、销售量与其他成本的关系
      println("\n3.5 咖啡的平均利润、销售量与其他成本的关系：")
      val costAnalysis = coffeeDF
        .agg(
          avg("Profit").alias("Avg_Profit"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          avg("Cogs").alias("Avg_Cogs"),
          avg("Marketing").alias("Avg_Marketing"),
          avg("Total_Expenses").alias("Avg_Total_Expenses"),
          corr("Profit", "Coffee_Sales").alias("Profit_Sales_Correlation"),
          corr("Coffee_Sales", "Marketing").alias("Sales_Marketing_Correlation"),
          corr("Profit", "Total_Expenses").alias("Profit_Expenses_Correlation")
        )

      costAnalysis.show()

      // 3.6 市场规模、市场地域与销售量的关系
      println("\n3.6 市场规模、市场地域与销售量的关系：")
      val marketAnalysis = coffeeDF
        .groupBy("Market_Size", "Market")
        .agg(
          sum("Coffee_Sales").alias("Total_Sales"),
          avg("Coffee_Sales").alias("Avg_Sales"),
          avg("Profit").alias("Avg_Profit"),
          count("*").alias("Record_Count")
        )
        .orderBy(desc("Total_Sales"))

      marketAnalysis.show()

      // 保存所有分析结果
      salesByState.coalesce(1).write.mode("overwrite").option("header", "true").csv("sales_by_state")
      salesByMarket.coalesce(1).write.mode("overwrite").option("header", "true").csv("sales_by_market")
      profitByProductType.coalesce(1).write.mode("overwrite").option("header", "true").csv("profit_by_product_type")
      marketAnalysis.coalesce(1).write.mode("overwrite").option("header", "true").csv("market_analysis")

      // 生成综合分析报告
      val reportData = Seq(
        "咖啡连锁店数据分析报告",
        s"分析时间：${java.time.LocalDateTime.now()}",
        s"数据记录总数：$totalRecords",
        "",
        "主要发现：",
        s"1. 总销售额最高的州：${salesByState.first().getString(0)}",
        s"2. 平均利润最高的产品类型：${profitByProductType.first().getString(0)}",
        s"3. 销售量最大的市场：${salesByMarket.first().getString(0)}",
        "",
        "详细分析结果已保存到以下目录：",
        "- product_ranking: 产品销售排名",
        "- state_ranking: 州销售排名",
        "- sales_by_state: 按州销售分析",
        "- sales_by_market: 按市场销售分析",
        "- profit_by_product_type: 按产品类型利润分析",
        "- market_analysis: 市场规模分析"
      )

      spark.sparkContext.parallelize(reportData).coalesce(1).saveAsTextFile("analysis_report")

      println("\n所有分析结果已保存完成！")

    } catch {
      case e: Exception =>
        println(s"数据处理过程中发生错误：${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
      println("\n程序执行完成！")
    }
  }
}
